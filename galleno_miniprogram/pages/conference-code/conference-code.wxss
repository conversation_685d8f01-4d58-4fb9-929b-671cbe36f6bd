/* conference-code.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 32rpx;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 会议信息横幅 */
.conference-banner {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  color: #ffffff;
}

.banner-icon {
  width: 128rpx;
  height: 128rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
}

.icon-key {
  font-size: 64rpx;
}

.conference-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.conference-location,
.conference-date {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

/* 识别码输入区域 */
.input-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.input-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.input-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.input-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 输入框容器 */
.input-container {
  margin-bottom: 48rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.input-wrapper {
  position: relative;
  border: 4rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  background-color: #ffffff;
  overflow: hidden;
}

.input-wrapper.focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
}

.input-wrapper.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}

.code-input {
  width: 100%;
  padding: 24rpx 80rpx 24rpx 32rpx;
  font-size: 36rpx;
  font-family: 'Courier New', monospace;
  text-align: center;
  letter-spacing: 4rpx;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: transparent;
  user-select: text;
  -webkit-user-select: text;
  color: #1f2937;
  line-height: 1.5;
  height: auto;
  min-height: 88rpx;
}

.input-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9ca3af;
}

.input-hint {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

.hint-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.hint-text {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 错误信息 */
.error-message {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  animation: shake 0.5s ease-in-out;
}

.error-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ef4444;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

/* 验证按钮 */
.verify-btn {
  width: 100%;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 32rpx;
}

.verify-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.verify-btn.loading {
  background: #6b7280;
}

.verify-btn::after {
  border: none;
}

/* 尝试次数 */
.attempts-info {
  text-align: center;
}

.attempts-text {
  font-size: 28rpx;
  color: #6b7280;
}

.attempts-count {
  font-size: 28rpx;
  font-weight: 600;
  color: #f59e0b;
}

.attempts-count.warning {
  color: #ef4444;
}

/* 帮助信息 */
.help-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.help-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.help-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #3b82f6;
}

.help-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.help-item {
  margin-bottom: 32rpx;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-question {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.help-answer {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 联系客服 */
.contact-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.contact-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.contact-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.contact-subtitle {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
}

.contact-btn {
  background: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.contact-btn::after {
  border: none;
}

.contact-icon {
  font-size: 24rpx;
}
