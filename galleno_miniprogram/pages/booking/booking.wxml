<!--booking.wxml-->
<view class="container">
  <!-- 预订信息摘要 -->
  <view class="booking-summary">
    <view class="summary-header">
      <text class="summary-title">预订信息</text>
    </view>
    
    <view class="summary-content">
      <view class="summary-row">
        <text class="summary-label">会议</text>
        <text class="summary-value">{{bookingData.conferenceTitle}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">房型</text>
        <text class="summary-value">{{bookingData.roomName}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">入住时间</text>
        <text class="summary-value">{{bookingData.checkinDate}} 至 {{bookingData.checkoutDate}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">住宿天数</text>
        <text class="summary-value">{{bookingData.nights}}晚</text>
      </view>
    </view>
  </view>

  <!-- 入住人信息 -->
  <view class="form-section">
    <view class="section-title">入住人信息</view>

    <!-- 简化测试输入框 -->
    <view class="form-group">
      <text class="form-label">简化测试输入框</text>
      <input style="border: 1px solid red; padding: 10px; background: white; color: black; font-size: 16px;"
             value="{{testValue}}"
             bindinput="onTestInput" />
      <text style="color: blue;">测试值: {{testValue}}</text>
    </view>
    
    <view class="form-group">
      <text class="form-label">姓名 <text class="required">*</text></text>
      <input class="form-input name-input {{errors.name ? 'error' : ''}}"
             type="text"
             placeholder="请输入真实姓名"
             placeholder-style="color: #9ca3af;"
             value="{{formData.name}}"
             cursor-spacing="10"
             confirm-type="next"
             adjust-position="{{true}}"
             auto-focus="{{false}}"
             selection-start="-1"
             selection-end="-1"
             hold-keyboard="{{false}}"
             bindinput="onInputChange"
             bindblur="onInputBlur"
             bindfocus="onInputFocus"
             data-field="name" />
      <text class="debug-text">调试: {{formData.name || '空值'}}</text>
      <text class="error-text" wx:if="{{errors.name}}">{{errors.name}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">手机号 <text class="required">*</text></text>
      <input class="form-input {{errors.phone ? 'error' : ''}}"
             type="number"
             placeholder="请输入手机号码"
             placeholder-style="color: #9ca3af;"
             value="{{formData.phone}}"
             maxlength="11"
             cursor-spacing="10"
             confirm-type="next"
             adjust-position="{{true}}"
             bindinput="onInputChange"
             data-field="phone" />
      <text class="debug-text">调试: {{formData.phone || '空值'}}</text>
      <text class="error-text" wx:if="{{errors.phone}}">{{errors.phone}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">身份证号 <text class="required">*</text></text>
      <input class="form-input {{errors.idCard ? 'error' : ''}}"
             type="idcard"
             placeholder="请输入身份证号码"
             placeholder-style="color: #9ca3af;"
             value="{{formData.idCard}}"
             maxlength="18"
             cursor-spacing="10"
             confirm-type="done"
             adjust-position="{{true}}"
             bindinput="onInputChange"
             data-field="idCard" />
      <text class="debug-text">调试: {{formData.idCard || '空值'}}</text>
      <text class="error-text" wx:if="{{errors.idCard}}">{{errors.idCard}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">特殊需求</text>
      <textarea class="form-textarea"
                placeholder="如有特殊需求请在此说明（选填）"
                placeholder-style="color: #9ca3af;"
                value="{{formData.specialRequests}}"
                cursor-spacing="10"
                adjust-position="{{true}}"
                bindinput="onInputChange"
                data-field="specialRequests"
                maxlength="200"
                show-confirm-bar="false" />
      <view class="textarea-counter">{{specialRequestsLength}}/200</view>
    </view>
  </view>

  <!-- 联系方式确认 -->
  <view class="form-section">
    <view class="section-title">联系方式</view>
    
    <view class="contact-info">
      <view class="contact-item">
        <text class="contact-label">联系人</text>
        <text class="contact-value">{{formData.name || '请先填写姓名'}}</text>
      </view>
      <view class="contact-item">
        <text class="contact-label">联系电话</text>
        <text class="contact-value">{{formData.phone || '请先填写手机号'}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="section-title">费用明细</view>
    
    <view class="cost-details">
      <view class="cost-row">
        <text class="cost-label">房费 ({{bookingData.roomPrice}}/晚 × {{bookingData.nights}}晚)</text>
        <text class="cost-value">¥{{bookingData.totalPrice}}</text>
      </view>
      <view class="cost-row">
        <text class="cost-label">服务费</text>
        <text class="cost-value">¥0</text>
      </view>
      <view class="cost-divider"></view>
      <view class="cost-row total">
        <text class="cost-label">总计</text>
        <text class="cost-value">¥{{bookingData.totalPrice}}</text>
      </view>
      <view class="cost-row deposit">
        <text class="cost-label">需支付定金</text>
        <text class="cost-value">¥200</text>
      </view>
    </view>
  </view>

  <!-- 预订条款 -->
  <view class="terms-section">
    <view class="terms-checkbox" bindtap="toggleTerms">
      <text class="checkbox {{agreedToTerms ? 'checked' : ''}}">{{agreedToTerms ? '✓' : ''}}</text>
      <text class="terms-text">我已阅读并同意</text>
      <text class="terms-link" bindtap="showTerms" catchtap="true">《预订条款》</text>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 底部支付栏 -->
<view class="payment-bar">
  <view class="payment-info">
    <text class="payment-label">需支付定金</text>
    <text class="payment-price">¥200</text>
  </view>
  <button class="payment-btn {{canPay ? '' : 'disabled'}}" 
          disabled="{{!canPay}}" 
          bindtap="proceedToPayment">
    立即支付
  </button>
</view>
