<!--booking.wxml-->
<view class="container">
  <!-- 预订信息摘要 -->
  <view class="booking-summary">
    <view class="summary-header">
      <text class="summary-title">预订信息</text>
    </view>
    
    <view class="summary-content">
      <view class="summary-row">
        <text class="summary-label">会议</text>
        <text class="summary-value">{{bookingData.conferenceTitle}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">房型</text>
        <text class="summary-value">{{bookingData.roomName}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">入住时间</text>
        <text class="summary-value">{{bookingData.checkinDate}} 至 {{bookingData.checkoutDate}}</text>
      </view>
      <view class="summary-row">
        <text class="summary-label">住宿天数</text>
        <text class="summary-value">{{bookingData.nights}}晚</text>
      </view>
    </view>
  </view>

  <!-- 入住人信息 -->
  <view class="form-section">
    <view class="section-title">入住人信息</view>

    <!-- 完全独立的测试输入框 -->
    <view style="margin: 20rpx; padding: 20rpx; border: 2rpx solid blue; background: #f0f0f0;">
      <text style="display: block; margin-bottom: 10rpx; color: blue; font-size: 28rpx;">独立测试输入框</text>
      <input style="width: 100%; height: 80rpx; border: 2rpx solid red; padding: 20rpx; font-size: 32rpx; color: black; background: white; box-sizing: border-box;"
             value="{{independentTestValue}}"
             bindinput="onIndependentTestInput" />
      <text style="display: block; margin-top: 10rpx; color: green; font-size: 24rpx;">独立测试值: {{independentTestValue}}</text>
    </view>

    <!-- 原有简化测试输入框 -->
    <view class="form-group">
      <text class="form-label">简化测试输入框</text>
      <input style="border: 1px solid red; padding: 10px; background: white; color: black; font-size: 16px;"
             value="{{testValue}}"
             bindinput="onTestInput" />
      <text style="color: blue;">测试值: {{testValue}}</text>
    </view>
    
    <view class="form-group">
      <text class="form-label">姓名 <text class="required">*</text></text>
      <input style="width: 100%; height: 80rpx; border: 2rpx solid #333; padding: 20rpx; font-size: 32rpx; color: #000; background: #fff; box-sizing: border-box;"
             type="text"
             placeholder="请输入真实姓名"
             value="{{formData.name}}"
             bindinput="onInputChange"
             data-field="name" />
      <text class="debug-text">调试: {{formData.name || '空值'}}</text>
      <text class="error-text" wx:if="{{errors.name}}">{{errors.name}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">手机号 <text class="required">*</text></text>
      <input style="width: 100%; height: 80rpx; border: 2rpx solid #333; padding: 20rpx; font-size: 32rpx; color: #000; background: #fff; box-sizing: border-box;"
             type="text"
             placeholder="请输入手机号码"
             value="{{formData.phone}}"
             bindinput="onInputChange"
             data-field="phone" />
      <text class="debug-text">调试: {{formData.phone || '空值'}}</text>
      <text class="error-text" wx:if="{{errors.phone}}">{{errors.phone}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">身份证号 <text class="required">*</text></text>
      <input style="width: 100%; height: 80rpx; border: 2rpx solid #333; padding: 20rpx; font-size: 32rpx; color: #000; background: #fff; box-sizing: border-box;"
             type="text"
             placeholder="请输入身份证号码"
             value="{{formData.idCard}}"
             bindinput="onInputChange"
             data-field="idCard" />
      <text class="debug-text">调试: {{formData.idCard || '空值'}}</text>
      <text class="error-text" wx:if="{{errors.idCard}}">{{errors.idCard}}</text>
    </view>

    <view class="form-group">
      <text class="form-label">特殊需求</text>
      <textarea class="form-textarea"
                placeholder="如有特殊需求请在此说明（选填）"
                placeholder-style="color: #9ca3af;"
                value="{{formData.specialRequests}}"
                cursor-spacing="10"
                adjust-position="{{true}}"
                bindinput="onInputChange"
                data-field="specialRequests"
                maxlength="200"
                show-confirm-bar="false" />
      <view class="textarea-counter">{{specialRequestsLength}}/200</view>
    </view>
  </view>

  <!-- 联系方式确认 -->
  <view class="form-section">
    <view class="section-title">联系方式</view>
    
    <view class="contact-info">
      <view class="contact-item">
        <text class="contact-label">联系人</text>
        <text class="contact-value">{{formData.name || '请先填写姓名'}}</text>
      </view>
      <view class="contact-item">
        <text class="contact-label">联系电话</text>
        <text class="contact-value">{{formData.phone || '请先填写手机号'}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="section-title">费用明细</view>
    
    <view class="cost-details">
      <view class="cost-row">
        <text class="cost-label">房费 ({{bookingData.roomPrice}}/晚 × {{bookingData.nights}}晚)</text>
        <text class="cost-value">¥{{bookingData.totalPrice}}</text>
      </view>
      <view class="cost-row">
        <text class="cost-label">服务费</text>
        <text class="cost-value">¥0</text>
      </view>
      <view class="cost-divider"></view>
      <view class="cost-row total">
        <text class="cost-label">总计</text>
        <text class="cost-value">¥{{bookingData.totalPrice}}</text>
      </view>
      <view class="cost-row deposit">
        <text class="cost-label">需支付定金</text>
        <text class="cost-value">¥200</text>
      </view>
    </view>
  </view>

  <!-- 预订条款 -->
  <view class="terms-section">
    <view class="terms-checkbox" bindtap="toggleTerms">
      <text class="checkbox {{agreedToTerms ? 'checked' : ''}}">{{agreedToTerms ? '✓' : ''}}</text>
      <text class="terms-text">我已阅读并同意</text>
      <text class="terms-link" bindtap="showTerms" catchtap="true">《预订条款》</text>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 底部支付栏 -->
<view class="payment-bar">
  <view class="payment-info">
    <text class="payment-label">需支付定金</text>
    <text class="payment-price">¥200</text>
  </view>
  <button class="payment-btn {{canPay ? '' : 'disabled'}}" 
          disabled="{{!canPay}}" 
          bindtap="proceedToPayment">
    立即支付
  </button>
</view>
