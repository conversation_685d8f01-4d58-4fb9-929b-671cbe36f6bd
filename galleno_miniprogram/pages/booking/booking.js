// booking.js
const util = require('../../utils/util.js')
const PaymentUtils = require('../../utils/payment.js')

Page({
  data: {
    bookingData: {},
    formData: {
      name: '',
      phone: '',
      idCard: '',
      specialRequests: ''
    },
    errors: {},
    agreedToTerms: false,
    canPay: false,
    testValue: '',
    independentTestValue: '',
    specialRequestsLength: 0
  },

  onLoad: function (options) {
    console.log('booking页面加载');

    // 获取预订数据
    const bookingData = wx.getStorageSync('bookingData');
    if (bookingData) {
      this.setData({
        bookingData: bookingData
      });
    } else {
      // 如果没有预订数据，返回上一页
      wx.showModal({
        title: '提示',
        content: '预订信息丢失，请重新选择',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }

    // 尝试获取用户信息
    this.getUserInfo();
  },

  onShow: function () {
    console.log('booking页面显示，当前表单数据:', this.data.formData);
  },

  onReady: function () {
    console.log('booking页面渲染完成，当前表单数据:', this.data.formData);

    // 测试数据更新
    setTimeout(() => {
      console.log('测试设置数据...');
      this.setData({
        'formData.name': '测试姓名',
        'formData.phone': '13800138000'
      }, () => {
        console.log('测试数据设置完成:', this.data.formData);
      });
    }, 1000);
  },

  // 获取用户信息
  getUserInfo: function () {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    console.log('获取到的用户信息:', userInfo);

    if (userInfo) {
      const updateData = {
        'formData.name': userInfo.name || '',
        'formData.phone': userInfo.phone || '',
        'formData.idCard': userInfo.idCard || ''
      };

      // 计算特殊需求长度
      updateData.specialRequestsLength = (updateData['formData.specialRequests'] || '').length;

      this.setData(updateData, () => {
        console.log('用户信息设置完成:', this.data.formData);
        this.validateForm();
      });
    } else {
      console.log('没有找到用户信息，使用空值');
      // 确保表单数据初始化
      this.setData({
        'formData.name': '',
        'formData.phone': '',
        'formData.idCard': '',
        'formData.specialRequests': '',
        specialRequestsLength: 0
      });
    }
  },

  // 输入框内容变化
  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    console.log(`输入框变化 - 字段: ${field}, 值: ${value}`);

    // 简化的数据更新方式
    const updateData = {};
    updateData[`formData.${field}`] = value;
    updateData[`errors.${field}`] = '';

    if (field === 'specialRequests') {
      updateData.specialRequestsLength = (value || '').length;
    }

    this.setData(updateData, () => {
      console.log(`数据更新完成 - ${field}: ${this.data.formData[field]}`);
    });

    this.validateForm();
  },

  // 输入框获得焦点
  onInputFocus: function (e) {
    const field = e.currentTarget.dataset.field;
    console.log(`输入框获得焦点: ${field}`);
  },

  // 输入框失去焦点
  onInputBlur: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    console.log(`输入框失去焦点: ${field}, 值: ${value}`);

    // 再次确保数据更新
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 表单验证
  validateForm: function () {
    const { name, phone, idCard } = this.data.formData;
    const errors = {};
    let isValid = true;

    // 姓名验证
    if (!name.trim()) {
      errors.name = '请输入姓名';
      isValid = false;
    } else if (name.trim().length < 2) {
      errors.name = '姓名至少2个字符';
      isValid = false;
    }

    // 手机号验证
    if (!phone.trim()) {
      errors.phone = '请输入手机号';
      isValid = false;
    } else if (!util.validatePhone(phone)) {
      errors.phone = '请输入正确的手机号';
      isValid = false;
    }

    // 身份证号验证
    if (!idCard.trim()) {
      errors.idCard = '请输入身份证号';
      isValid = false;
    } else if (!util.validateIdCard(idCard)) {
      errors.idCard = '请输入正确的身份证号';
      isValid = false;
    }

    this.setData({
      errors: errors,
      canPay: isValid && this.data.agreedToTerms
    });

    return isValid;
  },

  // 切换条款同意状态
  toggleTerms: function () {
    const agreedToTerms = !this.data.agreedToTerms;
    this.setData({
      agreedToTerms: agreedToTerms,
      canPay: this.validateForm() && agreedToTerms
    });
  },

  // 显示预订条款
  showTerms: function () {
    wx.showModal({
      title: '预订条款',
      content: '1. 预订成功后，定金不予退还\n2. 入住时需出示有效身份证件\n3. 如需取消预订，请提前24小时联系客服\n4. 房间价格可能因市场变化而调整\n5. 特殊需求我们将尽力满足，但不保证一定能够实现',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 前往支付
  proceedToPayment: function () {
    if (!this.data.canPay) {
      if (!this.validateForm()) {
        wx.showToast({
          title: '请完善必填信息',
          icon: 'none'
        });
        return;
      }
      if (!this.data.agreedToTerms) {
        wx.showToast({
          title: '请同意预订条款',
          icon: 'none'
        });
        return;
      }
    }

    // 保存用户信息到本地
    const userInfo = {
      name: this.data.formData.name,
      phone: this.data.formData.phone,
      idCard: this.data.formData.idCard
    };
    wx.setStorageSync('userInfo', userInfo);

    // 构建完整的订单数据
    const orderData = {
      ...this.data.bookingData,
      guestInfo: this.data.formData,
      orderTime: new Date().toISOString(),
      orderId: this.generateOrderId(),
      depositAmount: 0.01,
      status: 'pending'
    };

    // 保存订单数据
    wx.setStorageSync('currentOrder', orderData);

    // 调用微信支付
    this.requestPayment(orderData);
  },

  // 生成订单号
  generateOrderId: function () {
    return util.generateOrderId();
  },

  // 请求支付
  requestPayment: function (orderData) {
    // 构建支付请求参数
    const paymentData = {
      outTradeNo: orderData.orderId,
      body: `酒店预订-${orderData.roomName}`,
      totalFee: PaymentUtils.yuanToFen(orderData.depositAmount), // 将元转换为分
      detail: `${orderData.checkinDate}至${orderData.checkoutDate}，共${orderData.nights}晚`,
      attach: `conferenceId:${orderData.conferenceId},roomType:${orderData.roomType}`
    };

    console.log('开始创建支付订单', paymentData);

    // 调用支付工具类创建支付
    PaymentUtils.createPayment(paymentData)
      .then((res) => {
        console.log('支付成功', res);
        this.handlePaymentSuccess(orderData);
      })
      .catch((err) => {
        console.log('支付失败', err);
        this.handlePaymentFail(err);
      });
  },

  // 支付成功处理
  handlePaymentSuccess: function (orderData) {
    // 更新订单状态
    orderData.status = 'paid';
    orderData.paymentTime = new Date().toISOString();
    
    // 保存到本地存储
    wx.setStorageSync('currentOrder', orderData);
    
    // 添加到订单历史
    let orderHistory = wx.getStorageSync('orderHistory') || [];
    orderHistory.unshift(orderData);
    wx.setStorageSync('orderHistory', orderHistory);

    // 跳转到支付成功页面
    wx.redirectTo({
      url: '/pages/payment-success/payment-success'
    });
  },

  // 支付失败处理
  handlePaymentFail: function (error) {
    if (error.errMsg === 'requestPayment:fail cancel') {
      wx.showToast({
        title: '支付已取消',
        icon: 'none'
      });
    } else {
      wx.showModal({
        title: '支付失败',
        content: '支付过程中出现问题，请重试或联系客服',
        showCancel: true,
        cancelText: '重试',
        confirmText: '联系客服',
        success: (res) => {
          if (res.confirm) {
            this.contactService();
          }
        }
      });
    }
  },

  // 联系客服
  contactService: function () {
    wx.makePhoneCall({
      phoneNumber: '4008889999',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  // 测试输入处理
  onTestInput: function (e) {
    const value = e.detail.value;
    console.log('测试输入:', value);
    this.setData({
      testValue: value
    });
  },

  // 独立测试输入处理
  onIndependentTestInput: function (e) {
    const value = e.detail.value;
    console.log('独立测试输入:', value);
    this.setData({
      independentTestValue: value
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定 - 预订信息',
      path: '/pages/conference-list/conference-list'
    };
  }
});
